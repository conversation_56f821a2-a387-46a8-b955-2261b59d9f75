import {
  collection,
  doc,
  addDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  deleteField,
  query,
  orderBy,
  where,
  Timestamp,
  writeBatch,
  getDoc,
  limit
} from 'firebase/firestore';
import { firestore as db } from './firestoreConfig';
import { UserRole, UserProfile } from './services/UserService';

export interface ApprovalDelayEntry {
  id: string;
  reason: string;
  createdAt: Timestamp;
  createdBy?: string;
  createdByName?: string;
}

export enum SystemTaskStatus {
  NOT_STARTED = 'not_started',
  // Classification Data statuses
  DATA_REQUESTED = 'data_requested',
  DATA_RECEIVED = 'data_received',
  // Initial/Further Classification statuses
  IN_PROGRESS = 'in_progress',
  // Classification Review statuses
  DATA_SENT_AWAITING_REPLY = 'data_sent_awaiting_reply',
  MEETING_SCHEDULED = 'meeting_scheduled',
  MEETING_DONE = 'meeting_done',
  NO_REPLY_RECEIVED = 'no_reply_received',
  // Final Approval specific statuses
  APPROVAL_DELAYED = 'approval_delayed',
  DONE = 'done'
}

export interface System {
  id?: string;
  name: string;
  responsibleOwner: string;
  dba: string;
  email: string;
  group?: string; // Group number from 1-9
  consultantId?: string; // ID of the assigned consultant
  consultantName?: string; // Name of the assigned consultant
  consultantEmail?: string; // Email of the assigned consultant
  createdAt: Timestamp;
  updatedAt: Timestamp;
  // Task Status Fields - Updated for new task structure
  classificationDataStatus?: SystemTaskStatus;
  initialClassificationStatus?: SystemTaskStatus;
  furtherClassificationStatus?: SystemTaskStatus;
  classificationReviewStatus?: SystemTaskStatus;
  finalApprovalStatus?: SystemTaskStatus;
  // Task Start Dates - Updated for new task structure
  classificationDataStartDate?: Timestamp;
  initialClassificationStartDate?: Timestamp;
  furtherClassificationStartDate?: Timestamp;
  classificationReviewStartDate?: Timestamp;
  finalApprovalStartDate?: Timestamp;
  // Task End Dates - Updated for new task structure
  classificationDataEndDate?: Timestamp;
  initialClassificationEndDate?: Timestamp;
  furtherClassificationEndDate?: Timestamp;
  classificationReviewEndDate?: Timestamp;
  finalApprovalEndDate?: Timestamp;
  // Task Progress (0-100) - Updated for new task structure
  classificationDataProgress?: number;
  initialClassificationProgress?: number;
  furtherClassificationProgress?: number;
  classificationReviewProgress?: number;
  finalApprovalProgress?: number;
  // Task Expected Dates - Updated for new task structure
  classificationDataExpectedDate?: Timestamp;
  initialClassificationExpectedDate?: Timestamp;
  furtherClassificationExpectedDate?: Timestamp;
  classificationReviewExpectedDate?: Timestamp;
  finalApprovalExpectedDate?: Timestamp;
  // Classification Review specific fields
  classificationReviewNote?: string;
  classificationReviewMeetingId?: string; // Link to meeting in MeetingsService
  classificationReviewDataSentDate?: Timestamp; // For auto-status change after 2 days
  // Task Notes (max 20 words each) - Updated for new task structure
  classificationDataNote?: string;
  initialClassificationNote?: string;
  furtherClassificationNote?: string;
  finalApprovalNote?: string;
  // Task Links for attachment functionality
  furtherClassificationLink?: string;
  classificationReviewLink?: string;
  finalApprovalLink?: string;
  // Final Approval delay history
  finalApprovalDelayHistory?: ApprovalDelayEntry[];
  // System Links for reference
  dataSchemaLink?: string;
  classificationLink?: string;
  approvalEmailLink?: string;
}

export interface SystemData {
  id?: string;
  schemaName?: string;
  tableName: string;
  columnName: string;
  dataType: string;
  maxLength?: number | null;
  isNullable?: boolean | null;
  columnOrder?: number | null;
  lastSeek?: string | null;
  lastScan?: string | null;
  lastLookup?: string | null;
  lastUpdate?: string | null;
  importOrder?: number;
  createdAt: Timestamp;
  systemId: string;
  // AI Classification attributes
  tableType?: "system_table" | "data_table";
  dataCategory?: "customers" | "development_team";
  confidentialityLevel?: "Public" | "Confidential" | "Secret" | "Top Secret";
  confidentialityReasoning?: string;
  hasPersonalData?: boolean;
  personalDataReason?: string;
  personalDataReasoning?: string;
  classificationStatus?: "pending" | "table_classified" | "fully_classified";
  // Review attributes
  isReviewed?: boolean;
  reviewedBy?: string;
  reviewedAt?: string;
  // Personal Data Classification attributes
  personalDataType?: "direct" | "indirect" | "pseudonymous" | "anonymous";
  specialCategoryType?: "PII" | "PHI" | "PCI" | "Genetic" | "Biometric" | "none";
  personalDataClassificationStatus?: "pending" | "classified";
  // Audit Trail attribute
  auditTrail?: string; // "Yes" or "No"
  // ROPA Group Tag attribute
  groupTag?: string; // Group name for ROPA entity grouping
  // Needs Review attribute
  needsReview?: boolean;
  // Push to Client attribute
  pushToClient?: string; // "Yes" or "No"
  // Change tracking attributes for specification levels
  confidentialityLevelChanged?: boolean;
  confidentialityLevelOldValue?: string;
  confidentialityLevelNewValue?: string;
  hasPersonalDataChanged?: boolean;
  hasPersonalDataOldValue?: boolean;
  hasPersonalDataNewValue?: boolean;
  personalDataTypeChanged?: boolean;
  personalDataTypeOldValue?: string;
  personalDataTypeNewValue?: string;
  specialCategoryTypeChanged?: boolean;
  specialCategoryTypeOldValue?: string;
  specialCategoryTypeNewValue?: string;
  // DPIA Assessment attributes
  dpiaAssessmentId?: string;
  dpiaAssessedAt?: Timestamp;
  dpiaFinalRiskLevel?: "HIGH" | "MEDIUM" | "LOW";
  dpiaOverallImpact?: string;
  // Individual Harm Domain
  dpiaIndividualHarmRiskLevel?: "HIGH" | "MEDIUM" | "LOW";
  dpiaIndividualHarmIdentityRecreation?: string;
  dpiaIndividualHarmFinancialExploitation?: string;
  dpiaIndividualHarmPrivacyInvasion?: string;
  dpiaIndividualHarmPsychologicalImpact?: string;
  // Business Reputation Domain
  dpiaBusinessReputationRiskLevel?: "HIGH" | "MEDIUM" | "LOW";
  dpiaBusinessReputationTrustErosion?: string;
  dpiaBusinessReputationMediaCoverage?: string;
  dpiaBusinessReputationBrandDamage?: string;
  dpiaBusinessReputationCompetitiveDisadvantage?: string;
  // Financial Impact Domain
  dpiaFinancialImpactRiskLevel?: "HIGH" | "MEDIUM" | "LOW";
  dpiaFinancialImpactRegulatoryFines?: string;
  dpiaFinancialImpactLegalCosts?: string;
  dpiaFinancialImpactRemediationCosts?: string;
  dpiaFinancialImpactRevenueLoss?: string;
  // Operational Impact Domain
  dpiaOperationalImpactRiskLevel?: "HIGH" | "MEDIUM" | "LOW";
  dpiaOperationalImpactRecoveryTime?: string;
  dpiaOperationalImpactResourceDiversion?: string;
  dpiaOperationalImpactProcessBreakdown?: string;
  dpiaOperationalImpactCustomerService?: string;
  // Legal/Regulatory Domain
  dpiaLegalRegulatoryRiskLevel?: "HIGH" | "MEDIUM" | "LOW";
  dpiaLegalRegulatoryPdplViolations?: string;
  dpiaLegalRegulatorySdaiaRisk?: string;
  dpiaLegalRegulatoryCriminalLiability?: string;
  dpiaLegalRegulatoryInternationalIssues?: string;
}

export interface SystemContextPoint {
  id?: string;
  content: string; // Max 70 words
  tag: string; // e.g., "Business Statement", "Technical Requirement", etc.
  createdAt: Timestamp;
  updatedAt: Timestamp;
  systemId: string;
}

export interface SystemContext {
  id?: string;
  context: string; // Legacy field - will be deprecated
  contextPoints: SystemContextPoint[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  systemId: string;
}

export interface SystemService {
  id?: string;
  serviceName: string;
  serviceDescription: string;
  serviceBeneficiaries: string; // Dropdown: Citizens, Businesses, Government, All
  serviceAvailability: string; // Dropdown: 24/7, Business Hours, Scheduled, On-Demand
  serviceTime: string; // Dropdown: Instant, Minutes, Hours, Days, Weeks
  serviceFees: number; // Can be 0
  serviceSteps?: ServiceStep[]; // Optional
  serviceRequirements?: ServiceRequirement[]; // Optional
  createdAt: Timestamp;
  updatedAt: Timestamp;
  systemId: string;
}

export interface ServiceStep {
  id: string;
  stepNumber: number;
  title: string;
  description: string;
  estimatedTime?: string;
  isRequired: boolean;
}

export interface ServiceRequirement {
  id: string;
  title: string;
  description: string;
  type: string; // Dropdown: Document, Information, Payment, Authentication
  isRequired: boolean;
}

export interface DPIAAssessment {
  id?: string;
  attributeId: string;
  systemId: string;
  tableName: string;
  columnName: string;
  domainAnswers: {
    individualHarm: {
      riskLevel: "HIGH" | "MEDIUM" | "LOW";
      identityRecreation: string;
      financialExploitation: string;
      privacyInvasion: string;
      psychologicalImpact: string;
    };
    businessReputation: {
      riskLevel: "HIGH" | "MEDIUM" | "LOW";
      trustErosion: string;
      mediaCoverage: string;
      brandDamage: string;
      competitiveDisadvantage: string;
    };
    financialImpact: {
      riskLevel: "HIGH" | "MEDIUM" | "LOW";
      regulatoryFines: string;
      legalCosts: string;
      remediationCosts: string;
      revenueLoss: string;
    };
    operationalImpact: {
      riskLevel: "HIGH" | "MEDIUM" | "LOW";
      recoveryTime: string;
      resourceDiversion: string;
      processBreakdown: string;
      customerService: string;
    };
    legalRegulatory: {
      riskLevel: "HIGH" | "MEDIUM" | "LOW";
      pdplViolations: string;
      sdaiaRisk: string;
      criminalLiability: string;
      internationalIssues: string;
    };
  };
  finalRiskLevel: "HIGH" | "MEDIUM" | "LOW";
  overallImpact: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface SystemPrivacyPolicy {
  id?: string;
  content: string;
  language: 'en' | 'ar';
  systemId: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface SentenceAnalysis {
  sentence: string;
  domain: string;
  domainDescription: string;
  isCompliant: boolean;
  completenessScore: number;
  issues: string[];
  suggestions: string[];
}

export interface PolicyAnalysis {
  id?: string;
  sentences: SentenceAnalysis[];
  overallScore: number;
  missingDomains: string[];
  recommendations: string[];
  complianceLevel: 'Excellent' | 'Good' | 'Needs Improvement' | 'Poor';
  systemId: string;
  policyContent: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

const COLLECTION_NAME = 'systems';
const SYSTEM_DATA_SUBCOLLECTION = 'systemData';
const SYSTEM_CONTEXT_SUBCOLLECTION = 'systemContext';
const SYSTEM_SERVICES_SUBCOLLECTION = 'systemServices';
const SYSTEM_PRIVACY_POLICY_SUBCOLLECTION = 'privacyPolicies';
const SYSTEM_PRIVACY_ANALYSIS_SUBCOLLECTION = 'privacyAnalysis';

export class SystemsService {
  // Add a new system
  static async addSystem(systemData: Omit<System, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const docRef = await addDoc(collection(db, COLLECTION_NAME), {
        ...systemData,
        createdAt: now,
        updatedAt: now,
      });
      return docRef.id;
    } catch (error) {
      console.error('Error adding system:', error);
      throw new Error('Failed to add system');
    }
  }

  // Get all systems
  static async getSystems(): Promise<System[]> {
    try {
      const q = query(collection(db, COLLECTION_NAME), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as System[];
    } catch (error) {
      console.error('Error fetching systems:', error);
      throw new Error('Failed to fetch systems');
    }
  }

  // Get all systems with their data counts - OPTIMIZED VERSION
  static async getSystemsWithDataCounts(): Promise<(System & { dataCount: number })[]> {
    try {
      console.log('Starting optimized systems fetch...');
      const startTime = Date.now();

      const systems = await this.getSystems();
      console.log(`Fetched ${systems.length} systems in ${Date.now() - startTime}ms`);

      if (systems.length === 0) {
        return [];
      }

      // Use the optimized batch data count method
      const batchStartTime = Date.now();
      const dataCounts = await this.getBatchSystemDataCounts(systems.map(s => s.id!));
      console.log(`Fetched data counts for ${systems.length} systems in ${Date.now() - batchStartTime}ms`);

      // Combine systems with their data counts
      const systemsWithCounts = systems.map(system => ({
        ...system,
        dataCount: dataCounts[system.id!] || 0
      }));

      // Sort systems: those with data first, then by creation date
      const sortedSystems = systemsWithCounts.sort((a, b) => {
        // First, sort by whether they have data (systems with data come first)
        if (a.dataCount > 0 && b.dataCount === 0) return -1;
        if (a.dataCount === 0 && b.dataCount > 0) return 1;

        // If both have data or both don't have data, sort by creation date (newest first)
        const aDate = a.createdAt instanceof Date ? a.createdAt : a.createdAt.toDate();
        const bDate = b.createdAt instanceof Date ? b.createdAt : b.createdAt.toDate();
        return bDate.getTime() - aDate.getTime();
      });

      console.log(`Total systems fetch completed in ${Date.now() - startTime}ms`);
      return sortedSystems;
    } catch (error) {
      console.error('Error fetching systems with data counts:', error);
      throw new Error('Failed to fetch systems with data counts');
    }
  }

  // NEW: Optimized batch method to check if systems have data (boolean only)
  static async getBatchSystemDataCounts(systemIds: string[]): Promise<Record<string, number>> {
    try {
      if (systemIds.length === 0) {
        return {};
      }

      console.log(`Checking data existence for ${systemIds.length} systems...`);

      // Use Promise.allSettled to handle individual failures gracefully
      const checkPromises = systemIds.map(async (systemId) => {
        try {
          const systemDataCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION);
          // OPTIMIZED: Use limit(1) to just check if any data exists - much faster than counting all
          const q = query(systemDataCollection, limit(1));
          const querySnapshot = await getDocs(q);
          return { systemId, hasData: !querySnapshot.empty };
        } catch (error) {
          console.warn(`Failed to check data existence for system ${systemId}:`, error);
          return { systemId, hasData: false };
        }
      });

      const results = await Promise.allSettled(checkPromises);

      // Build the result object - convert boolean to number for compatibility
      const dataCounts: Record<string, number> = {};
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const { systemId, hasData } = result.value;
          dataCounts[systemId] = hasData ? 1 : 0; // 1 means has data, 0 means no data
        } else {
          // Handle failed promises
          dataCounts[systemIds[index]] = 0;
          console.warn(`Failed to check data for system ${systemIds[index]}:`, result.reason);
        }
      });

      return dataCounts;
    } catch (error) {
      console.error('Error in batch data existence check:', error);
      // Return no data for all systems rather than failing completely
      const fallbackCounts: Record<string, number> = {};
      systemIds.forEach(id => fallbackCounts[id] = 0);
      return fallbackCounts;
    }
  }

  // NEW: Fast method to check if a system has any data (boolean)
  static async systemHasData(systemId: string): Promise<boolean> {
    try {
      const systemDataCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION);
      // Use limit(1) for maximum efficiency - just check if any document exists
      const q = query(systemDataCollection, limit(1));
      const querySnapshot = await getDocs(q);
      return !querySnapshot.empty;
    } catch (error) {
      console.error('Error checking if system has data:', error);
      return false;
    }
  }

  // Update a system
  static async updateSystem(id: string, systemData: Partial<Omit<System, 'id' | 'createdAt'>>): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await updateDoc(docRef, {
        ...systemData,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating system:', error);
      throw new Error('Failed to update system');
    }
  }

  // Update system task status with dates and progress - Updated for new task structure
  static async updateSystemTaskStatus(
    systemId: string,
    taskType: 'classificationDataStatus' | 'initialClassificationStatus' | 'furtherClassificationStatus' | 'classificationReviewStatus' | 'finalApprovalStatus',
    status: SystemTaskStatus,
    startDate?: Date,
    endDate?: Date,
    progress?: number,
    taskLink?: string, // For task links
    taskNote?: string,
    meetingId?: string // For Classification Review
  ): Promise<void> {
    try {
      // Validate that status is not undefined to prevent Firebase errors
      if (status === undefined || status === null) {
        throw new Error(`Status cannot be undefined or null for task type: ${taskType}`);
      }

      const docRef = doc(db, COLLECTION_NAME, systemId);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const updateData: Record<string, any> = {
        [taskType]: status,
        updatedAt: Timestamp.now(),
      };

      // Get the base field name (remove 'Status' suffix)
      const baseFieldName = taskType.replace('Status', '');

      // Handle start date
      if (startDate) {
        updateData[`${baseFieldName}StartDate`] = Timestamp.fromDate(startDate);
      }

      // Handle end date
      if (endDate) {
        updateData[`${baseFieldName}EndDate`] = Timestamp.fromDate(endDate);
      }

      // Handle progress
      if (progress !== undefined) {
        updateData[`${baseFieldName}Progress`] = Math.max(0, Math.min(100, progress));
      }

      // Handle task links
      if (taskLink !== undefined) {
        const linkFieldName = `${baseFieldName}Link`;
        updateData[linkFieldName] = taskLink || null; // Use null instead of empty string
      }

      // Handle Classification Review specific fields
      if (taskType === 'classificationReviewStatus') {
        if (meetingId !== undefined) {
          updateData['classificationReviewMeetingId'] = meetingId || null;
        }
        // Set data sent date for auto-status tracking
        if (status === SystemTaskStatus.DATA_SENT_AWAITING_REPLY) {
          updateData['classificationReviewDataSentDate'] = Timestamp.now();
        }
      }

      // Handle task notes
      if (taskNote !== undefined) {
        const noteFieldName = `${baseFieldName}Note`;
        updateData[noteFieldName] = taskNote || null; // Use null instead of empty string
      }

      // Auto-set dates based on status
      const now = Timestamp.now();
      if (status === SystemTaskStatus.IN_PROGRESS && !startDate) {
        updateData[`${baseFieldName}StartDate`] = now;
      }
      if (status === SystemTaskStatus.DONE && !endDate) {
        updateData[`${baseFieldName}EndDate`] = now;
        updateData[`${baseFieldName}Progress`] = 100; // Auto-complete progress
      }

      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error updating system task status:', error);
      throw new Error('Failed to update system task status');
    }
  }

  // Auto-update systems that have been waiting for replies for more than 2 days
  static async checkAndUpdateOverdueReplies(): Promise<void> {
    try {
      const systems = await this.getSystems();
      const now = new Date();
      const twoDaysAgo = new Date(now.getTime() - (2 * 24 * 60 * 60 * 1000));

      for (const system of systems) {
        if (
          system.classificationReviewStatus === SystemTaskStatus.DATA_SENT_AWAITING_REPLY &&
          system.classificationReviewDataSentDate &&
          system.classificationReviewDataSentDate.toDate() < twoDaysAgo
        ) {
          await this.updateSystemTaskStatus(
            system.id!,
            'classificationReviewStatus',
            SystemTaskStatus.NO_REPLY_RECEIVED
          );
        }
      }
    } catch (error) {
      console.error('Error checking overdue replies:', error);
      throw new Error('Failed to check overdue replies');
    }
  }

  // Add approval delay entry to system
  static async addApprovalDelayEntry(
    systemId: string,
    reason: string,
    createdBy?: string,
    createdByName?: string
  ): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, systemId);
      const systemDoc = await getDoc(docRef);

      if (!systemDoc.exists()) {
        throw new Error('System not found');
      }

      const system = systemDoc.data() as System;
      const currentDelayHistory = system.finalApprovalDelayHistory || [];

      // Create delay entry with only defined fields to avoid Firestore undefined errors
      const newDelayEntry: ApprovalDelayEntry = {
        id: Date.now().toString(),
        reason: reason.trim(),
        createdAt: Timestamp.now()
      };

      // Only add optional fields if they have values
      if (createdBy) {
        newDelayEntry.createdBy = createdBy;
      }
      if (createdByName) {
        newDelayEntry.createdByName = createdByName;
      }

      const updatedDelayHistory = [...currentDelayHistory, newDelayEntry];

      // Create update object with only defined values
      const updateData = {
        finalApprovalStatus: SystemTaskStatus.APPROVAL_DELAYED,
        finalApprovalDelayHistory: updatedDelayHistory,
        finalApprovalNote: reason.trim(), // Keep the latest reason in the note field for backward compatibility
        updatedAt: Timestamp.now()
      };

      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error adding approval delay entry:', error);
      throw new Error('Failed to add approval delay entry');
    }
  }

  // Update existing approval delay entry
  static async updateApprovalDelayEntry(
    systemId: string,
    entryId: string,
    newReason: string
  ): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, systemId);
      const systemDoc = await getDoc(docRef);

      if (!systemDoc.exists()) {
        throw new Error('System not found');
      }

      const system = systemDoc.data() as System;
      const currentDelayHistory = system.finalApprovalDelayHistory || [];

      // Find and update the specific entry
      const updatedDelayHistory = currentDelayHistory.map(entry => {
        if (entry.id === entryId) {
          return {
            ...entry,
            reason: newReason.trim()
          };
        }
        return entry;
      });

      // Update the latest reason in the note field for backward compatibility
      const latestEntry = updatedDelayHistory[updatedDelayHistory.length - 1];

      const updateData = {
        finalApprovalDelayHistory: updatedDelayHistory,
        finalApprovalNote: latestEntry?.reason || '',
        updatedAt: Timestamp.now()
      };

      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error updating approval delay entry:', error);
      throw new Error('Failed to update approval delay entry');
    }
  }

  // Delete approval delay entry
  static async deleteApprovalDelayEntry(
    systemId: string,
    entryId: string
  ): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, systemId);
      const systemDoc = await getDoc(docRef);

      if (!systemDoc.exists()) {
        throw new Error('System not found');
      }

      const system = systemDoc.data() as System;
      const currentDelayHistory = system.finalApprovalDelayHistory || [];

      // Remove the specific entry
      const updatedDelayHistory = currentDelayHistory.filter(entry => entry.id !== entryId);

      // Update the latest reason in the note field for backward compatibility
      const latestEntry = updatedDelayHistory[updatedDelayHistory.length - 1];

      // If no delay entries remain, change status back to NOT_STARTED
      const updateData = {
        finalApprovalDelayHistory: updatedDelayHistory,
        finalApprovalNote: latestEntry?.reason || '',
        updatedAt: Timestamp.now(),
        ...(updatedDelayHistory.length === 0 && { finalApprovalStatus: SystemTaskStatus.NOT_STARTED })
      };

      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error deleting approval delay entry:', error);
      throw new Error('Failed to delete approval delay entry');
    }
  }

  // Check if a system is completed
  // A system is considered completed only when "Final Approval Received?" has "Received Approval" status
  static isSystemCompleted(system: System): boolean {
    return system.finalApprovalStatus === SystemTaskStatus.DONE;
  }

  // Get systems with pagination
  static async getSystemsPaginated(page: number = 1, limit: number = 10): Promise<{ systems: System[], total: number, totalPages: number }> {
    try {
      const q = query(collection(db, COLLECTION_NAME), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);

      const allSystems = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as System[];

      const total = allSystems.length;
      const totalPages = Math.ceil(total / limit);
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const systems = allSystems.slice(startIndex, endIndex);

      return { systems, total, totalPages };
    } catch (error) {
      console.error('Error fetching systems with pagination:', error);
      throw new Error('Failed to fetch systems with pagination');
    }
  }

  // Delete a system
  static async deleteSystem(id: string): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, id);
      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting system:', error);
      throw new Error('Failed to delete system');
    }
  }

  // Add system data in batches
  static async addSystemDataBatch(systemId: string, systemDataArray: Omit<SystemData, 'id' | 'createdAt' | 'systemId'>[]): Promise<void> {
    try {
      const batch = writeBatch(db);
      const now = Timestamp.now();

      systemDataArray.forEach((data) => {
        const systemDataRef = doc(collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION));
        batch.set(systemDataRef, {
          ...data,
          systemId,
          createdAt: now,
        });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error adding system data batch:', error);
      throw new Error('Failed to add system data batch');
    }
  }

  // Replace all system data (delete existing and add new)
  static async replaceSystemDataBatch(systemId: string, systemDataArray: Omit<SystemData, 'id' | 'createdAt' | 'systemId'>[]): Promise<void> {
    try {
      // First, delete all existing system data
      await this.deleteAllSystemData(systemId);

      // Then add the new data
      await this.addSystemDataBatch(systemId, systemDataArray);
    } catch (error) {
      console.error('Error replacing system data batch:', error);
      throw new Error('Failed to replace system data batch');
    }
  }

  // Get system data
  static async getSystemData(systemId: string, limitCount?: number): Promise<SystemData[]> {
    try {
      const systemDataCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION);
      
      // Get all data first, then sort in memory to handle missing importOrder fields
      const querySnapshot = await getDocs(systemDataCollection);
      
      let systemData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SystemData[];

      // Sort by importOrder (ascending), fallback to createdAt for legacy data
      systemData.sort((a, b) => {
        if (a.importOrder !== undefined && b.importOrder !== undefined) {
          return a.importOrder - b.importOrder;
        }
        if (a.importOrder !== undefined && b.importOrder === undefined) {
          return -1; // New data with importOrder comes first
        }
        if (a.importOrder === undefined && b.importOrder !== undefined) {
          return 1; // Old data without importOrder comes last
        }
        // Both are legacy data, sort by createdAt
        return a.createdAt.toMillis() - b.createdAt.toMillis();
      });

      // Apply limit if specified
      if (limitCount) {
        systemData = systemData.slice(0, limitCount);
      }
      
      return systemData;
    } catch (error) {
      console.error('Error fetching system data:', error);
      throw new Error('Failed to fetch system data');
    }
  }

  // Get system data count - OPTIMIZED with caching
  static async getSystemDataCount(systemId: string): Promise<number> {
    try {
      // Check cache first
      const cacheKey = `dataCount_${systemId}`;
      const cached = this.getFromCache(cacheKey);
      if (cached !== null && typeof cached === 'number') {
        return cached;
      }

      const systemDataCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION);
      const querySnapshot = await getDocs(systemDataCollection);
      const count = querySnapshot.size;

      // Cache the result for 5 minutes
      this.setCache(cacheKey, count, 5 * 60 * 1000);

      return count;
    } catch (error) {
      console.error('Error fetching system data count:', error);
      return 0;
    }
  }

  // NEW: Simple in-memory cache for performance optimization
  private static cache = new Map<string, { value: unknown; expiry: number }>();

  private static getFromCache(key: string): unknown {
    const cached = this.cache.get(key);
    if (cached && cached.expiry > Date.now()) {
      return cached.value;
    }
    if (cached) {
      this.cache.delete(key); // Remove expired entry
    }
    return null;
  }

  private static setCache(key: string, value: unknown, ttlMs: number): void {
    this.cache.set(key, {
      value,
      expiry: Date.now() + ttlMs
    });
  }

  // NEW: Clear cache for a specific system (useful after data updates)
  static clearSystemCache(systemId: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => key.includes(systemId));
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // NEW: Clear all cache
  static clearAllCache(): void {
    this.cache.clear();
  }

  // Delete all system data
  static async deleteAllSystemData(systemId: string): Promise<void> {
    try {
      const systemDataCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION);
      const querySnapshot = await getDocs(systemDataCollection);

      const batch = writeBatch(db);
      querySnapshot.docs.forEach((doc) => {
        batch.delete(doc.ref);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error deleting system data:', error);
      throw new Error('Failed to delete system data');
    }
  }

  // Delete individual system data document
  static async deleteSystemDataDocument(systemId: string, documentId: string): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!documentId || !documentId.trim()) {
        throw new Error('Document ID is required');
      }

      const docRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_DATA_SUBCOLLECTION, documentId.trim());

      // Check if document exists first
      const docSnap = await getDoc(docRef);
      if (!docSnap.exists()) {
        throw new Error(`Document ${documentId} not found`);
      }

      await deleteDoc(docRef);
    } catch (error) {
      console.error('Error deleting system data document:', error);
      throw new Error('Failed to delete system data document');
    }
  }

  // Delete multiple system data documents in batch
  static async deleteSystemDataBatch(systemId: string, documentIds: string[]): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!documentIds || documentIds.length === 0) {
        throw new Error('Document IDs are required');
      }

      console.log(`Starting batch delete for ${documentIds.length} documents in system ${systemId}`);

      const batch = writeBatch(db);
      const failedDeletes: string[] = [];
      const successfulDeletes: string[] = [];

      for (const documentId of documentIds) {
        try {
          if (!documentId || !documentId.trim()) {
            console.warn(`Invalid document ID: ${documentId}`);
            failedDeletes.push(documentId);
            continue;
          }

          const docRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_DATA_SUBCOLLECTION, documentId.trim());

          // Check if document exists first
          const docSnap = await getDoc(docRef);
          if (docSnap.exists()) {
            batch.delete(docRef);
            successfulDeletes.push(documentId);
          } else {
            console.warn(`Document ${documentId} does not exist, skipping delete`);
            failedDeletes.push(documentId);
          }
        } catch (docError) {
          console.error(`Error preparing delete for document ${documentId}:`, docError);
          failedDeletes.push(documentId);
        }
      }

      // Only commit if there are valid deletes
      if (successfulDeletes.length > 0) {
        await batch.commit();
        console.log(`Successfully deleted ${successfulDeletes.length} documents`);
      } else {
        console.warn('No valid documents to delete');
      }

      // Log any failed deletes
      if (failedDeletes.length > 0) {
        console.warn(`Failed to delete ${failedDeletes.length} documents:`, failedDeletes);
        throw new Error(`Failed to delete ${failedDeletes.length} out of ${documentIds.length} documents`);
      }

    } catch (error) {
      console.error('Error deleting system data batch:', error);
      throw error instanceof Error ? error : new Error('Failed to delete system data batch');
    }
  }

  // Update individual system data document with classification and DPIA attributes
  static async updateSystemDataDocument(
    systemId: string,
    documentId: string,
    updates: Partial<Pick<SystemData, 'tableType' | 'dataCategory' | 'confidentialityLevel' | 'confidentialityReasoning' | 'hasPersonalData' | 'personalDataReason' | 'personalDataReasoning' | 'classificationStatus' | 'isReviewed' | 'reviewedBy' | 'reviewedAt' | 'personalDataType' | 'specialCategoryType' | 'personalDataClassificationStatus' | 'auditTrail' | 'groupTag' | 'needsReview' | 'pushToClient' | 'confidentialityLevelChanged' | 'confidentialityLevelOldValue' | 'confidentialityLevelNewValue' | 'hasPersonalDataChanged' | 'hasPersonalDataOldValue' | 'hasPersonalDataNewValue' | 'personalDataTypeChanged' | 'personalDataTypeOldValue' | 'personalDataTypeNewValue' | 'specialCategoryTypeChanged' | 'specialCategoryTypeOldValue' | 'specialCategoryTypeNewValue' | 'dpiaAssessmentId' | 'dpiaAssessedAt' | 'dpiaFinalRiskLevel' | 'dpiaOverallImpact' | 'dpiaIndividualHarmIdentityRecreation' | 'dpiaIndividualHarmFinancialExploitation' | 'dpiaIndividualHarmPrivacyInvasion' | 'dpiaIndividualHarmPsychologicalImpact' | 'dpiaBusinessReputationTrustErosion' | 'dpiaBusinessReputationMediaCoverage' | 'dpiaBusinessReputationBrandDamage' | 'dpiaBusinessReputationCompetitiveDisadvantage' | 'dpiaFinancialImpactRegulatoryFines' | 'dpiaFinancialImpactLegalCosts' | 'dpiaFinancialImpactRemediationCosts' | 'dpiaFinancialImpactRevenueLoss' | 'dpiaOperationalImpactRecoveryTime' | 'dpiaOperationalImpactResourceDiversion' | 'dpiaOperationalImpactProcessBreakdown' | 'dpiaOperationalImpactCustomerService' | 'dpiaLegalRegulatoryPdplViolations' | 'dpiaLegalRegulatorySdaiaRisk' | 'dpiaLegalRegulatoryCriminalLiability' | 'dpiaLegalRegulatoryInternationalIssues'>>
  ): Promise<void> {
    try {
      // Remove undefined values to avoid Firebase errors
      const cleanUpdates = Object.fromEntries(
        Object.entries(updates).filter(([, value]) => value !== undefined)
      );

      const docRef = doc(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION, documentId);

      // Check if document exists first
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        console.warn(`Document ${documentId} does not exist, cannot update`);
        throw new Error(`Document ${documentId} not found`);
      }

      await updateDoc(docRef, cleanUpdates);
    } catch (error) {
      console.error('Error updating system data document:', error);
      if (error instanceof Error && error.message.includes('not found')) {
        throw error; // Re-throw document not found errors
      }
      throw new Error('Failed to update system data document');
    }
  }

  // Update multiple system data documents in batch
  static async updateSystemDataBatch(
    systemId: string,
    updates: Array<{
      documentId: string;
      data: Partial<Pick<SystemData, 'tableType' | 'dataCategory' | 'confidentialityLevel' | 'confidentialityReasoning' | 'hasPersonalData' | 'personalDataReason' | 'personalDataReasoning' | 'classificationStatus' | 'isReviewed' | 'reviewedBy' | 'reviewedAt' | 'personalDataType' | 'specialCategoryType' | 'personalDataClassificationStatus' | 'auditTrail' | 'groupTag' | 'needsReview' | 'pushToClient' | 'confidentialityLevelChanged' | 'confidentialityLevelOldValue' | 'confidentialityLevelNewValue' | 'hasPersonalDataChanged' | 'hasPersonalDataOldValue' | 'hasPersonalDataNewValue' | 'personalDataTypeChanged' | 'personalDataTypeOldValue' | 'personalDataTypeNewValue' | 'specialCategoryTypeChanged' | 'specialCategoryTypeOldValue' | 'specialCategoryTypeNewValue' | 'dpiaAssessmentId' | 'dpiaAssessedAt' | 'dpiaFinalRiskLevel' | 'dpiaOverallImpact' | 'dpiaIndividualHarmIdentityRecreation' | 'dpiaIndividualHarmFinancialExploitation' | 'dpiaIndividualHarmPrivacyInvasion' | 'dpiaIndividualHarmPsychologicalImpact' | 'dpiaBusinessReputationTrustErosion' | 'dpiaBusinessReputationMediaCoverage' | 'dpiaBusinessReputationBrandDamage' | 'dpiaBusinessReputationCompetitiveDisadvantage' | 'dpiaFinancialImpactRegulatoryFines' | 'dpiaFinancialImpactLegalCosts' | 'dpiaFinancialImpactRemediationCosts' | 'dpiaFinancialImpactRevenueLoss' | 'dpiaOperationalImpactRecoveryTime' | 'dpiaOperationalImpactResourceDiversion' | 'dpiaOperationalImpactProcessBreakdown' | 'dpiaOperationalImpactCustomerService' | 'dpiaLegalRegulatoryPdplViolations' | 'dpiaLegalRegulatorySdaiaRisk' | 'dpiaLegalRegulatoryCriminalLiability' | 'dpiaLegalRegulatoryInternationalIssues'>>;
    }>
  ): Promise<void> {
    try {
      console.log(`Starting batch update for ${updates.length} documents in system ${systemId}`);

      const batch = writeBatch(db);
      const failedUpdates: string[] = [];
      const successfulUpdates: string[] = [];

      for (const { documentId, data } of updates) {
        try {
          if (!documentId || !documentId.trim()) {
            console.warn(`Invalid document ID: ${documentId}`);
            failedUpdates.push(documentId);
            continue;
          }

          // Remove undefined values to avoid Firebase errors
          const cleanData = Object.fromEntries(
            Object.entries(data).filter(([, value]) => value !== undefined)
          );

          const docRef = doc(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION, documentId);

          // Check if document exists first
          const docSnap = await getDoc(docRef);

          if (docSnap.exists()) {
            // Document exists, use update
            batch.update(docRef, cleanData);
            successfulUpdates.push(documentId);
          } else {
            // Document doesn't exist, skip this update and log it
            console.warn(`Document ${documentId} does not exist, skipping update`);
            failedUpdates.push(documentId);
          }
        } catch (_docError) {
          console.error(`Error preparing update for document ${documentId}:`, _docError);
          failedUpdates.push(documentId);
        }
      }

      // Only commit if there are valid updates
      if (successfulUpdates.length > 0) {
        await batch.commit();
        console.log(`Successfully updated ${successfulUpdates.length} documents`);
      } else {
        console.warn('No valid documents to update');
      }

      // Log any failed updates
      if (failedUpdates.length > 0) {
        console.warn(`Failed to update ${failedUpdates.length} documents:`, failedUpdates);
        throw new Error(`Failed to update ${failedUpdates.length} out of ${updates.length} documents`);
      }

    } catch (error) {
      console.error('Error updating system data batch:', error);
      throw error instanceof Error ? error : new Error('Failed to update system data batch');
    }
  }

  // Add system context point
  static async addSystemContextPoint(systemId: string, content: string, tag: string): Promise<string> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      
      const now = Timestamp.now();
      const contextCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_CONTEXT_SUBCOLLECTION);
      
      const contextPoint: SystemContextPoint = {
        content: content.trim(),
        tag: tag.trim(),
        systemId: systemId.trim(),
        createdAt: now,
        updatedAt: now,
      };
      
      const docRef = await addDoc(contextCollection, contextPoint);
      return docRef.id;
    } catch (error) {
      console.error('Error adding system context point:', error);
      throw new Error('Failed to add system context point');
    }
  }

  // Update system context point
  static async updateSystemContextPoint(systemId: string, pointId: string, content: string, tag: string): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!pointId || !pointId.trim()) {
        throw new Error('Point ID is required');
      }
      
      const contextRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_CONTEXT_SUBCOLLECTION, pointId.trim());
      await updateDoc(contextRef, {
        content: content.trim(),
        tag: tag.trim(),
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating system context point:', error);
      throw new Error('Failed to update system context point');
    }
  }

  // Delete system context point
  static async deleteSystemContextPoint(systemId: string, pointId: string): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!pointId || !pointId.trim()) {
        throw new Error('Point ID is required');
      }
      
      const contextRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_CONTEXT_SUBCOLLECTION, pointId.trim());
      await deleteDoc(contextRef);
    } catch (error) {
      console.error('Error deleting system context point:', error);
      throw new Error('Failed to delete system context point');
    }
  }

  // Save system context (legacy method - kept for backward compatibility)
  static async saveSystemContext(systemId: string, context: string): Promise<void> {
    try {
      const now = Timestamp.now();
      const contextCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_CONTEXT_SUBCOLLECTION);
      
      // Check if context already exists
      const existingContext = await getDocs(contextCollection);
      
      if (existingContext.empty) {
        // Create new context
        await addDoc(contextCollection, {
          context,
          contextPoints: [],
          systemId,
          createdAt: now,
          updatedAt: now,
        });
      } else {
        // Update existing context (should only be one)
        const contextDoc = existingContext.docs[0];
        await updateDoc(contextDoc.ref, {
          context,
          updatedAt: now,
        });
      }
    } catch (error) {
      console.error('Error saving system context:', error);
      throw new Error('Failed to save system context');
    }
  }

  // Get system context points
  static async getSystemContextPoints(systemId: string): Promise<SystemContextPoint[]> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      
      const contextCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_CONTEXT_SUBCOLLECTION);
      const q = query(contextCollection, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SystemContextPoint[];
    } catch (error) {
      console.error('Error fetching system context points:', error);
      throw new Error('Failed to fetch system context points');
    }
  }

  // Get system context (legacy method - kept for backward compatibility)
  static async getSystemContext(systemId: string): Promise<SystemContext | null> {
    try {
      const contextCollection = collection(db, COLLECTION_NAME, systemId, SYSTEM_CONTEXT_SUBCOLLECTION);
      const querySnapshot = await getDocs(contextCollection);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      const contextDoc = querySnapshot.docs[0];
      return {
        id: contextDoc.id,
        ...contextDoc.data()
      } as SystemContext;
    } catch (error) {
      console.error('Error fetching system context:', error);
      return null;
    }
  }

  // DPIA Methods

  // Save DPIA assessment results directly to attribute collection
  static async saveDPIAAssessment(
    systemId: string, 
    assessments: Omit<DPIAAssessment, 'id' | 'createdAt' | 'updatedAt'>[]
  ): Promise<void> {
    try {
      const batch = writeBatch(db);
      const now = Timestamp.now();
      
      assessments.forEach((assessment) => {
        const attributeRef = doc(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION, assessment.attributeId);
        
        // Map DPIA assessment data to SystemData DPIA fields
        const dpiaData = {
          dpiaAssessmentId: `dpia_${assessment.attributeId}_${now.toMillis()}`,
          dpiaAssessedAt: now,
          dpiaFinalRiskLevel: assessment.finalRiskLevel,
          dpiaOverallImpact: assessment.overallImpact,
          // Individual Harm Domain
          dpiaIndividualHarmRiskLevel: assessment.domainAnswers.individualHarm.riskLevel,
          dpiaIndividualHarmIdentityRecreation: assessment.domainAnswers.individualHarm.identityRecreation,
          dpiaIndividualHarmFinancialExploitation: assessment.domainAnswers.individualHarm.financialExploitation,
          dpiaIndividualHarmPrivacyInvasion: assessment.domainAnswers.individualHarm.privacyInvasion,
          dpiaIndividualHarmPsychologicalImpact: assessment.domainAnswers.individualHarm.psychologicalImpact,
          // Business Reputation Domain
          dpiaBusinessReputationRiskLevel: assessment.domainAnswers.businessReputation.riskLevel,
          dpiaBusinessReputationTrustErosion: assessment.domainAnswers.businessReputation.trustErosion,
          dpiaBusinessReputationMediaCoverage: assessment.domainAnswers.businessReputation.mediaCoverage,
          dpiaBusinessReputationBrandDamage: assessment.domainAnswers.businessReputation.brandDamage,
          dpiaBusinessReputationCompetitiveDisadvantage: assessment.domainAnswers.businessReputation.competitiveDisadvantage,
          // Financial Impact Domain
          dpiaFinancialImpactRiskLevel: assessment.domainAnswers.financialImpact.riskLevel,
          dpiaFinancialImpactRegulatoryFines: assessment.domainAnswers.financialImpact.regulatoryFines,
          dpiaFinancialImpactLegalCosts: assessment.domainAnswers.financialImpact.legalCosts,
          dpiaFinancialImpactRemediationCosts: assessment.domainAnswers.financialImpact.remediationCosts,
          dpiaFinancialImpactRevenueLoss: assessment.domainAnswers.financialImpact.revenueLoss,
          // Operational Impact Domain
          dpiaOperationalImpactRiskLevel: assessment.domainAnswers.operationalImpact.riskLevel,
          dpiaOperationalImpactRecoveryTime: assessment.domainAnswers.operationalImpact.recoveryTime,
          dpiaOperationalImpactResourceDiversion: assessment.domainAnswers.operationalImpact.resourceDiversion,
          dpiaOperationalImpactProcessBreakdown: assessment.domainAnswers.operationalImpact.processBreakdown,
          dpiaOperationalImpactCustomerService: assessment.domainAnswers.operationalImpact.customerService,
          // Legal/Regulatory Domain
          dpiaLegalRegulatoryRiskLevel: assessment.domainAnswers.legalRegulatory.riskLevel,
          dpiaLegalRegulatoryPdplViolations: assessment.domainAnswers.legalRegulatory.pdplViolations,
          dpiaLegalRegulatorySdaiaRisk: assessment.domainAnswers.legalRegulatory.sdaiaRisk,
          dpiaLegalRegulatoryCriminalLiability: assessment.domainAnswers.legalRegulatory.criminalLiability,
          dpiaLegalRegulatoryInternationalIssues: assessment.domainAnswers.legalRegulatory.internationalIssues,
          // Update classification status
          classificationStatus: 'fully_classified' as const
        };
        
        batch.update(attributeRef, dpiaData);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error saving DPIA assessments:', error);
      throw new Error('Failed to save DPIA assessments');
    }
  }

  // Get DPIA assessments for system from attribute collection
  static async getDPIAAssessments(systemId: string): Promise<DPIAAssessment[]> {
    try {
      const allData = await this.getSystemData(systemId);
      
      // Filter only attributes with DPIA assessments and convert to DPIAAssessment format
      return allData
        .filter(item => item.dpiaAssessmentId && item.dpiaFinalRiskLevel)
        .map(item => ({
          id: item.dpiaAssessmentId!,
          attributeId: item.id!,
          systemId: item.systemId,
          tableName: item.tableName,
          columnName: item.columnName,
          domainAnswers: {
            individualHarm: {
              riskLevel: item.dpiaIndividualHarmRiskLevel || 'LOW',
              identityRecreation: item.dpiaIndividualHarmIdentityRecreation || '',
              financialExploitation: item.dpiaIndividualHarmFinancialExploitation || '',
              privacyInvasion: item.dpiaIndividualHarmPrivacyInvasion || '',
              psychologicalImpact: item.dpiaIndividualHarmPsychologicalImpact || ''
            },
            businessReputation: {
              riskLevel: item.dpiaBusinessReputationRiskLevel || 'LOW',
              trustErosion: item.dpiaBusinessReputationTrustErosion || '',
              mediaCoverage: item.dpiaBusinessReputationMediaCoverage || '',
              brandDamage: item.dpiaBusinessReputationBrandDamage || '',
              competitiveDisadvantage: item.dpiaBusinessReputationCompetitiveDisadvantage || ''
            },
            financialImpact: {
              riskLevel: item.dpiaFinancialImpactRiskLevel || 'LOW',
              regulatoryFines: item.dpiaFinancialImpactRegulatoryFines || '',
              legalCosts: item.dpiaFinancialImpactLegalCosts || '',
              remediationCosts: item.dpiaFinancialImpactRemediationCosts || '',
              revenueLoss: item.dpiaFinancialImpactRevenueLoss || ''
            },
            operationalImpact: {
              riskLevel: item.dpiaOperationalImpactRiskLevel || 'LOW',
              recoveryTime: item.dpiaOperationalImpactRecoveryTime || '',
              resourceDiversion: item.dpiaOperationalImpactResourceDiversion || '',
              processBreakdown: item.dpiaOperationalImpactProcessBreakdown || '',
              customerService: item.dpiaOperationalImpactCustomerService || ''
            },
            legalRegulatory: {
              riskLevel: item.dpiaLegalRegulatoryRiskLevel || 'LOW',
              pdplViolations: item.dpiaLegalRegulatoryPdplViolations || '',
              sdaiaRisk: item.dpiaLegalRegulatorySdaiaRisk || '',
              criminalLiability: item.dpiaLegalRegulatoryCriminalLiability || '',
              internationalIssues: item.dpiaLegalRegulatoryInternationalIssues || ''
            }
          },
          finalRiskLevel: item.dpiaFinalRiskLevel!,
          overallImpact: item.dpiaOverallImpact || '',
          createdAt: item.dpiaAssessedAt || item.createdAt,
          updatedAt: item.dpiaAssessedAt || item.createdAt
        })) as DPIAAssessment[];
    } catch (error) {
      console.error('Error fetching DPIA assessments:', error);
      throw new Error('Failed to fetch DPIA assessments');
    }
  }

  // Get assessed personal data (those with DPIA results)
  static async getAssessedPersonalData(systemId: string): Promise<SystemData[]> {
    try {
      const allData = await this.getSystemData(systemId);
      
      // Filter personal data that has been assessed (has DPIA data)
      return allData.filter(item => 
        item.hasPersonalData === true && 
        item.dpiaAssessmentId && 
        item.dpiaFinalRiskLevel
      );
    } catch (error) {
      console.error('Error fetching assessed personal data:', error);
      throw new Error('Failed to fetch assessed personal data');
    }
  }

  // Get pending personal data (no DPIA assessment yet)  
  static async getPendingPersonalData(systemId: string): Promise<SystemData[]> {
    try {
      const allData = await this.getSystemData(systemId);
      
      // Filter personal data that hasn't been assessed yet (no DPIA data)
      return allData.filter(item => 
        item.hasPersonalData === true && 
        (!item.dpiaAssessmentId || !item.dpiaFinalRiskLevel)
      );
    } catch (error) {
      console.error('Error fetching pending personal data:', error);
      throw new Error('Failed to fetch pending personal data');
    }
  }

  // Delete DPIA assessment from attribute
  static async deleteDPIAAssessment(systemId: string, attributeId: string): Promise<void> {
    try {
      const attributeRef = doc(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION, attributeId);
      
      // Remove all DPIA fields and reset classification status
      await updateDoc(attributeRef, {
        dpiaAssessmentId: null,
        dpiaAssessedAt: null,
        dpiaFinalRiskLevel: null,
        dpiaOverallImpact: null,
        // Individual Harm Domain
        dpiaIndividualHarmIdentityRecreation: null,
        dpiaIndividualHarmFinancialExploitation: null,
        dpiaIndividualHarmPrivacyInvasion: null,
        dpiaIndividualHarmPsychologicalImpact: null,
        // Business Reputation Domain
        dpiaBusinessReputationTrustErosion: null,
        dpiaBusinessReputationMediaCoverage: null,
        dpiaBusinessReputationBrandDamage: null,
        dpiaBusinessReputationCompetitiveDisadvantage: null,
        // Financial Impact Domain
        dpiaFinancialImpactRegulatoryFines: null,
        dpiaFinancialImpactLegalCosts: null,
        dpiaFinancialImpactRemediationCosts: null,
        dpiaFinancialImpactRevenueLoss: null,
        // Operational Impact Domain
        dpiaOperationalImpactRecoveryTime: null,
        dpiaOperationalImpactResourceDiversion: null,
        dpiaOperationalImpactProcessBreakdown: null,
        dpiaOperationalImpactCustomerService: null,
        // Legal/Regulatory Domain
        dpiaLegalRegulatoryPdplViolations: null,
        dpiaLegalRegulatorySdaiaRisk: null,
        dpiaLegalRegulatoryCriminalLiability: null,
        dpiaLegalRegulatoryInternationalIssues: null,
        // Reset classification status
        classificationStatus: 'table_classified'
      });
    } catch (error) {
      console.error('Error deleting DPIA assessment:', error);
      throw new Error('Failed to delete DPIA assessment');
    }
  }

  // Update DPIA assessment in attribute
  static async updateDPIAAssessment(
    systemId: string, 
    attributeId: string, 
    updates: Partial<Omit<DPIAAssessment, 'id' | 'createdAt'>>
  ): Promise<void> {
    try {
      const attributeRef = doc(db, COLLECTION_NAME, systemId, SYSTEM_DATA_SUBCOLLECTION, attributeId);
      
      // Map updates to SystemData DPIA fields
      const dpiaUpdates: Record<string, string | null> = {};
      
      if (updates.finalRiskLevel) {
        dpiaUpdates.dpiaFinalRiskLevel = updates.finalRiskLevel;
      }
      
      if (updates.overallImpact) {
        dpiaUpdates.dpiaOverallImpact = updates.overallImpact;
      }
      
      if (updates.domainAnswers) {
        // Individual Harm Domain
        if (updates.domainAnswers.individualHarm) {
          if (updates.domainAnswers.individualHarm.identityRecreation) {
            dpiaUpdates.dpiaIndividualHarmIdentityRecreation = updates.domainAnswers.individualHarm.identityRecreation;
          }
          if (updates.domainAnswers.individualHarm.financialExploitation) {
            dpiaUpdates.dpiaIndividualHarmFinancialExploitation = updates.domainAnswers.individualHarm.financialExploitation;
          }
          if (updates.domainAnswers.individualHarm.privacyInvasion) {
            dpiaUpdates.dpiaIndividualHarmPrivacyInvasion = updates.domainAnswers.individualHarm.privacyInvasion;
          }
          if (updates.domainAnswers.individualHarm.psychologicalImpact) {
            dpiaUpdates.dpiaIndividualHarmPsychologicalImpact = updates.domainAnswers.individualHarm.psychologicalImpact;
          }
        }
        
        // Business Reputation Domain
        if (updates.domainAnswers.businessReputation) {
          if (updates.domainAnswers.businessReputation.trustErosion) {
            dpiaUpdates.dpiaBusinessReputationTrustErosion = updates.domainAnswers.businessReputation.trustErosion;
          }
          if (updates.domainAnswers.businessReputation.mediaCoverage) {
            dpiaUpdates.dpiaBusinessReputationMediaCoverage = updates.domainAnswers.businessReputation.mediaCoverage;
          }
          if (updates.domainAnswers.businessReputation.brandDamage) {
            dpiaUpdates.dpiaBusinessReputationBrandDamage = updates.domainAnswers.businessReputation.brandDamage;
          }
          if (updates.domainAnswers.businessReputation.competitiveDisadvantage) {
            dpiaUpdates.dpiaBusinessReputationCompetitiveDisadvantage = updates.domainAnswers.businessReputation.competitiveDisadvantage;
          }
        }
        
        // Financial Impact Domain
        if (updates.domainAnswers.financialImpact) {
          if (updates.domainAnswers.financialImpact.regulatoryFines) {
            dpiaUpdates.dpiaFinancialImpactRegulatoryFines = updates.domainAnswers.financialImpact.regulatoryFines;
          }
          if (updates.domainAnswers.financialImpact.legalCosts) {
            dpiaUpdates.dpiaFinancialImpactLegalCosts = updates.domainAnswers.financialImpact.legalCosts;
          }
          if (updates.domainAnswers.financialImpact.remediationCosts) {
            dpiaUpdates.dpiaFinancialImpactRemediationCosts = updates.domainAnswers.financialImpact.remediationCosts;
          }
          if (updates.domainAnswers.financialImpact.revenueLoss) {
            dpiaUpdates.dpiaFinancialImpactRevenueLoss = updates.domainAnswers.financialImpact.revenueLoss;
          }
        }
        
        // Operational Impact Domain
        if (updates.domainAnswers.operationalImpact) {
          if (updates.domainAnswers.operationalImpact.recoveryTime) {
            dpiaUpdates.dpiaOperationalImpactRecoveryTime = updates.domainAnswers.operationalImpact.recoveryTime;
          }
          if (updates.domainAnswers.operationalImpact.resourceDiversion) {
            dpiaUpdates.dpiaOperationalImpactResourceDiversion = updates.domainAnswers.operationalImpact.resourceDiversion;
          }
          if (updates.domainAnswers.operationalImpact.processBreakdown) {
            dpiaUpdates.dpiaOperationalImpactProcessBreakdown = updates.domainAnswers.operationalImpact.processBreakdown;
          }
          if (updates.domainAnswers.operationalImpact.customerService) {
            dpiaUpdates.dpiaOperationalImpactCustomerService = updates.domainAnswers.operationalImpact.customerService;
          }
        }
        
        // Legal/Regulatory Domain
        if (updates.domainAnswers.legalRegulatory) {
          if (updates.domainAnswers.legalRegulatory.pdplViolations) {
            dpiaUpdates.dpiaLegalRegulatoryPdplViolations = updates.domainAnswers.legalRegulatory.pdplViolations;
          }
          if (updates.domainAnswers.legalRegulatory.sdaiaRisk) {
            dpiaUpdates.dpiaLegalRegulatorySdaiaRisk = updates.domainAnswers.legalRegulatory.sdaiaRisk;
          }
          if (updates.domainAnswers.legalRegulatory.criminalLiability) {
            dpiaUpdates.dpiaLegalRegulatoryCriminalLiability = updates.domainAnswers.legalRegulatory.criminalLiability;
          }
          if (updates.domainAnswers.legalRegulatory.internationalIssues) {
            dpiaUpdates.dpiaLegalRegulatoryInternationalIssues = updates.domainAnswers.legalRegulatory.internationalIssues;
          }
        }
      }
      
      if (Object.keys(dpiaUpdates).length > 0) {
        await updateDoc(attributeRef, dpiaUpdates);
      }
    } catch (error) {
      console.error('Error updating DPIA assessment:', error);
      throw new Error('Failed to update DPIA assessment');
    }
  }

  // Get all personal data (both assessed and pending)
  static async getAllPersonalData(systemId: string): Promise<SystemData[]> {
    try {
      const allData = await this.getSystemData(systemId);

      // Filter only personal data
      return allData.filter(item => item.hasPersonalData === true);
    } catch (error) {
      console.error('Error fetching all personal data:', error);
      throw new Error('Failed to fetch all personal data');
    }
  }

  // System Services Methods

  // Add a new service to a system
  static async addSystemService(systemId: string, serviceData: Omit<SystemService, 'id' | 'createdAt' | 'updatedAt' | 'systemId'>): Promise<string> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }

      const now = Timestamp.now();
      const servicesCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_SERVICES_SUBCOLLECTION);

      const docRef = await addDoc(servicesCollection, {
        ...serviceData,
        systemId: systemId.trim(),
        createdAt: now,
        updatedAt: now,
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding system service:', error);
      throw new Error('Failed to add system service');
    }
  }

  // Get all services for a system
  static async getSystemServices(systemId: string): Promise<SystemService[]> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }

      const servicesCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_SERVICES_SUBCOLLECTION);
      const q = query(servicesCollection, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SystemService[];
    } catch (error) {
      console.error('Error fetching system services:', error);
      throw new Error('Failed to fetch system services');
    }
  }

  // Update a system service
  static async updateSystemService(systemId: string, serviceId: string, updates: Partial<Omit<SystemService, 'id' | 'createdAt' | 'systemId'>>): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!serviceId || !serviceId.trim()) {
        throw new Error('Service ID is required');
      }

      const serviceRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_SERVICES_SUBCOLLECTION, serviceId.trim());
      await updateDoc(serviceRef, {
        ...updates,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating system service:', error);
      throw new Error('Failed to update system service');
    }
  }

  // Delete a system service
  static async deleteSystemService(systemId: string, serviceId: string): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!serviceId || !serviceId.trim()) {
        throw new Error('Service ID is required');
      }

      const serviceRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_SERVICES_SUBCOLLECTION, serviceId.trim());
      await deleteDoc(serviceRef);
    } catch (error) {
      console.error('Error deleting system service:', error);
      throw new Error('Failed to delete system service');
    }
  }

  // Get a specific system service
  static async getSystemService(systemId: string, serviceId: string): Promise<SystemService | null> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!serviceId || !serviceId.trim()) {
        throw new Error('Service ID is required');
      }

      const serviceRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_SERVICES_SUBCOLLECTION, serviceId.trim());
      const serviceDoc = await getDoc(serviceRef);

      if (serviceDoc.exists()) {
        return {
          id: serviceDoc.id,
          ...serviceDoc.data()
        } as SystemService;
      }

      return null;
    } catch (error) {
      console.error('Error fetching system service:', error);
      throw new Error('Failed to fetch system service');
    }
  }

  // Privacy Policy Methods

  // Add or update privacy policy for a system (only one policy per system)
  static async addSystemPrivacyPolicy(systemId: string, policyData: Omit<SystemPrivacyPolicy, 'id' | 'createdAt' | 'updatedAt' | 'systemId'>): Promise<string> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }

      const now = Timestamp.now();
      const privacyPolicyCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_POLICY_SUBCOLLECTION);

      // Check if a policy already exists for this language
      const existingPolicies = await getDocs(privacyPolicyCollection);
      const existingPolicy = existingPolicies.docs.find(doc => {
        const data = doc.data() as SystemPrivacyPolicy;
        return data.language === policyData.language;
      });

      if (existingPolicy) {
        // Update existing policy
        await updateDoc(existingPolicy.ref, {
          content: policyData.content,
          updatedAt: now,
        });
        return existingPolicy.id;
      } else {
        // Create new policy
        const docRef = await addDoc(privacyPolicyCollection, {
          ...policyData,
          systemId: systemId.trim(),
          createdAt: now,
          updatedAt: now,
        });
        return docRef.id;
      }
    } catch (error) {
      console.error('Error adding system privacy policy:', error);
      throw new Error('Failed to add system privacy policy');
    }
  }

  // Get all privacy policies for a system
  static async getSystemPrivacyPolicies(systemId: string): Promise<SystemPrivacyPolicy[]> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }

      const privacyPolicyCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_POLICY_SUBCOLLECTION);
      const q = query(privacyPolicyCollection, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SystemPrivacyPolicy[];
    } catch (error) {
      console.error('Error fetching system privacy policies:', error);
      throw new Error('Failed to fetch system privacy policies');
    }
  }

  // Update a system privacy policy
  static async updateSystemPrivacyPolicy(systemId: string, policyId: string, updates: Partial<Omit<SystemPrivacyPolicy, 'id' | 'createdAt' | 'systemId'>>): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!policyId || !policyId.trim()) {
        throw new Error('Policy ID is required');
      }

      const policyRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_POLICY_SUBCOLLECTION, policyId.trim());
      await updateDoc(policyRef, {
        ...updates,
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error updating system privacy policy:', error);
      throw new Error('Failed to update system privacy policy');
    }
  }

  // Delete a system privacy policy
  static async deleteSystemPrivacyPolicy(systemId: string, policyId: string): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!policyId || !policyId.trim()) {
        throw new Error('Policy ID is required');
      }

      const policyRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_POLICY_SUBCOLLECTION, policyId.trim());
      await deleteDoc(policyRef);
    } catch (error) {
      console.error('Error deleting system privacy policy:', error);
      throw new Error('Failed to delete system privacy policy');
    }
  }

  // Get a specific system privacy policy
  static async getSystemPrivacyPolicy(systemId: string, policyId: string): Promise<SystemPrivacyPolicy | null> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }
      if (!policyId || !policyId.trim()) {
        throw new Error('Policy ID is required');
      }

      const policyRef = doc(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_POLICY_SUBCOLLECTION, policyId.trim());
      const policyDoc = await getDoc(policyRef);

      if (policyDoc.exists()) {
        return {
          id: policyDoc.id,
          ...policyDoc.data()
        } as SystemPrivacyPolicy;
      }

      return null;
    } catch (error) {
      console.error('Error fetching system privacy policy:', error);
      throw new Error('Failed to fetch system privacy policy');
    }
  }

  // Get privacy policy by language
  static async getSystemPrivacyPolicyByLanguage(systemId: string, language: 'en' | 'ar'): Promise<SystemPrivacyPolicy | null> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }

      const privacyPolicyCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_POLICY_SUBCOLLECTION);
      const q = query(privacyPolicyCollection, orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);

      const policies = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as SystemPrivacyPolicy[];

      // Find the most recent policy for the specified language
      const policyForLanguage = policies.find(policy => policy.language === language);
      return policyForLanguage || null;
    } catch (error) {
      console.error('Error fetching system privacy policy by language:', error);
      throw new Error('Failed to fetch system privacy policy by language');
    }
  }

  // Privacy Policy Analysis Methods

  // Save privacy policy analysis
  static async savePrivacyPolicyAnalysis(systemId: string, analysisData: Omit<PolicyAnalysis, 'id' | 'createdAt' | 'updatedAt' | 'systemId'>): Promise<string> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }

      const now = Timestamp.now();
      const analysisCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_ANALYSIS_SUBCOLLECTION);

      // Delete existing analysis first (only keep the latest)
      const existingAnalysis = await getDocs(analysisCollection);
      const deletePromises = existingAnalysis.docs.map(doc => deleteDoc(doc.ref));
      await Promise.all(deletePromises);

      // Add new analysis
      const docRef = await addDoc(analysisCollection, {
        ...analysisData,
        systemId: systemId.trim(),
        createdAt: now,
        updatedAt: now,
      });

      return docRef.id;
    } catch (error) {
      console.error('Error saving privacy policy analysis:', error);
      throw new Error('Failed to save privacy policy analysis');
    }
  }

  // Get the latest privacy policy analysis
  static async getPrivacyPolicyAnalysis(systemId: string): Promise<PolicyAnalysis | null> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }

      const analysisCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_ANALYSIS_SUBCOLLECTION);
      const q = query(analysisCollection, orderBy('createdAt', 'desc'), limit(1));
      const querySnapshot = await getDocs(q);

      if (querySnapshot.empty) {
        return null;
      }

      const doc = querySnapshot.docs[0];
      return {
        id: doc.id,
        ...doc.data()
      } as PolicyAnalysis;
    } catch (error) {
      console.error('Error fetching privacy policy analysis:', error);
      throw new Error('Failed to fetch privacy policy analysis');
    }
  }

  // Delete privacy policy analysis
  static async deletePrivacyPolicyAnalysis(systemId: string): Promise<void> {
    try {
      if (!systemId || !systemId.trim()) {
        throw new Error('System ID is required');
      }

      const analysisCollection = collection(db, COLLECTION_NAME, systemId.trim(), SYSTEM_PRIVACY_ANALYSIS_SUBCOLLECTION);
      const querySnapshot = await getDocs(analysisCollection);

      const deletePromises = querySnapshot.docs.map(doc => deleteDoc(doc.ref));
      await Promise.all(deletePromises);
    } catch (error) {
      console.error('Error deleting privacy policy analysis:', error);
      throw new Error('Failed to delete privacy policy analysis');
    }
  }

  // Consultant Management Methods

  // Get all consultants (users with consultant role)
  static async getConsultants(): Promise<UserProfile[]> {
    try {
      const usersCollection = collection(db, 'users');
      const consultantsQuery = query(
        usersCollection,
        where('role', '==', UserRole.CONSULTANT),
        orderBy('displayName')
      );

      const querySnapshot = await getDocs(consultantsQuery);
      const consultants: UserProfile[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        consultants.push({
          uid: doc.id,
          email: data.email,
          displayName: data.displayName,
          photoURL: data.photoURL,
          role: data.role,
          createdAt: data.createdAt?.toDate() || new Date(),
          disabled: data.disabled || false
        });
      });

      return consultants;
    } catch (error) {
      console.error('Error fetching consultants:', error);
      throw new Error('Failed to fetch consultants');
    }
  }

  // Assign consultant to system
  static async assignConsultantToSystem(
    systemId: string,
    consultantId: string,
    consultantName: string,
    consultantEmail: string
  ): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, systemId);
      const updateData: Record<string, string | Timestamp | undefined> = {
        updatedAt: Timestamp.now(),
      };

      // Only add fields that have values
      if (consultantId) {
        updateData.consultantId = consultantId;
      }
      if (consultantName) {
        updateData.consultantName = consultantName;
      }
      if (consultantEmail) {
        updateData.consultantEmail = consultantEmail;
      }

      await updateDoc(docRef, updateData);
    } catch (error) {
      console.error('Error assigning consultant to system:', error);
      throw new Error('Failed to assign consultant to system');
    }
  }

  // Remove consultant from system
  static async removeConsultantFromSystem(systemId: string): Promise<void> {
    try {
      const docRef = doc(db, COLLECTION_NAME, systemId);
      await updateDoc(docRef, {
        consultantId: deleteField(),
        consultantName: deleteField(),
        consultantEmail: deleteField(),
        updatedAt: Timestamp.now(),
      });
    } catch (error) {
      console.error('Error removing consultant from system:', error);
      throw new Error('Failed to remove consultant from system');
    }
  }
}